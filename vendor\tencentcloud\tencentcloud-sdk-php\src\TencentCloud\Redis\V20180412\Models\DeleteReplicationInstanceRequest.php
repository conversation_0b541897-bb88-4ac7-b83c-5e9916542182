<?php
/*
 * Copyright (c) 2017-2018 THL A29 Limited, a Tencent company. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
namespace TencentCloud\Redis\*********\Models;
use TencentCloud\Common\AbstractModel;

/**
 * DeleteReplicationInstance请求参数结构体
 *
 * @method string getGroupId() 获取复制组ID
 * @method void setGroupId(string $GroupId) 设置复制组ID
 * @method string getInstanceId() 获取实例ID
 * @method void setInstanceId(string $InstanceId) 设置实例ID
 * @method boolean getSyncType() 获取数据同步类型，true:需要数据强同步,false:不需要强同步，仅限删除主实例
 * @method void setSyncType(boolean $SyncType) 设置数据同步类型，true:需要数据强同步,false:不需要强同步，仅限删除主实例
 */
class DeleteReplicationInstanceRequest extends AbstractModel
{
    /**
     * @var string 复制组ID
     */
    public $GroupId;

    /**
     * @var string 实例ID
     */
    public $InstanceId;

    /**
     * @var boolean 数据同步类型，true:需要数据强同步,false:不需要强同步，仅限删除主实例
     */
    public $SyncType;

    /**
     * @param string $GroupId 复制组ID
     * @param string $InstanceId 实例ID
     * @param boolean $SyncType 数据同步类型，true:需要数据强同步,false:不需要强同步，仅限删除主实例
     */
    function __construct()
    {

    }

    /**
     * For internal only. DO NOT USE IT.
     */
    public function deserialize($param)
    {
        if ($param === null) {
            return;
        }
        if (array_key_exists("GroupId",$param) and $param["GroupId"] !== null) {
            $this->GroupId = $param["GroupId"];
        }

        if (array_key_exists("InstanceId",$param) and $param["InstanceId"] !== null) {
            $this->InstanceId = $param["InstanceId"];
        }

        if (array_key_exists("SyncType",$param) and $param["SyncType"] !== null) {
            $this->SyncType = $param["SyncType"];
        }
    }
}
