<?php
/*
 * Copyright (c) 2017-2018 THL A29 Limited, a Tencent company. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
namespace TencentCloud\Mrs\*********\Models;
use TencentCloud\Common\AbstractModel;

/**
 * 药物用法用量
 *
 * @method string getValue() 获取值
注意：此字段可能返回 null，表示取不到有效值。
 * @method void setValue(string $Value) 设置值
注意：此字段可能返回 null，表示取不到有效值。
 * @method string getSingleMeasurement() 获取单次计量
注意：此字段可能返回 null，表示取不到有效值。
 * @method void setSingleMeasurement(string $SingleMeasurement) 设置单次计量
注意：此字段可能返回 null，表示取不到有效值。
 * @method string getFrequency() 获取频次
注意：此字段可能返回 null，表示取不到有效值。
 * @method void setFrequency(string $Frequency) 设置频次
注意：此字段可能返回 null，表示取不到有效值。
 * @method string getDrugDeliveryRoute() 获取给药途径
注意：此字段可能返回 null，表示取不到有效值。
 * @method void setDrugDeliveryRoute(string $DrugDeliveryRoute) 设置给药途径
注意：此字段可能返回 null，表示取不到有效值。
 */
class DosageBlock extends AbstractModel
{
    /**
     * @var string 值
注意：此字段可能返回 null，表示取不到有效值。
     */
    public $Value;

    /**
     * @var string 单次计量
注意：此字段可能返回 null，表示取不到有效值。
     */
    public $SingleMeasurement;

    /**
     * @var string 频次
注意：此字段可能返回 null，表示取不到有效值。
     */
    public $Frequency;

    /**
     * @var string 给药途径
注意：此字段可能返回 null，表示取不到有效值。
     */
    public $DrugDeliveryRoute;

    /**
     * @param string $Value 值
注意：此字段可能返回 null，表示取不到有效值。
     * @param string $SingleMeasurement 单次计量
注意：此字段可能返回 null，表示取不到有效值。
     * @param string $Frequency 频次
注意：此字段可能返回 null，表示取不到有效值。
     * @param string $DrugDeliveryRoute 给药途径
注意：此字段可能返回 null，表示取不到有效值。
     */
    function __construct()
    {

    }

    /**
     * For internal only. DO NOT USE IT.
     */
    public function deserialize($param)
    {
        if ($param === null) {
            return;
        }
        if (array_key_exists("Value",$param) and $param["Value"] !== null) {
            $this->Value = $param["Value"];
        }

        if (array_key_exists("SingleMeasurement",$param) and $param["SingleMeasurement"] !== null) {
            $this->SingleMeasurement = $param["SingleMeasurement"];
        }

        if (array_key_exists("Frequency",$param) and $param["Frequency"] !== null) {
            $this->Frequency = $param["Frequency"];
        }

        if (array_key_exists("DrugDeliveryRoute",$param) and $param["DrugDeliveryRoute"] !== null) {
            $this->DrugDeliveryRoute = $param["DrugDeliveryRoute"];
        }
    }
}
