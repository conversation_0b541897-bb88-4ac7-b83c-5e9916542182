<?php
/*
 * Copyright (c) 2017-2018 THL A29 Limited, a Tencent company. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
namespace TencentCloud\Redis\*********\Models;
use TencentCloud\Common\AbstractModel;

/**
 * DescribeProjectSecurityGroup请求参数结构体
 *
 * @method integer getProjectId() 获取0:默认项目；-1 所有项目; >0: 特定项目
 * @method void setProjectId(integer $ProjectId) 设置0:默认项目；-1 所有项目; >0: 特定项目
 * @method string getSecurityGroupId() 获取安全组Id
 * @method void setSecurityGroupId(string $SecurityGroupId) 设置安全组Id
 */
class DescribeProjectSecurityGroupRequest extends AbstractModel
{
    /**
     * @var integer 0:默认项目；-1 所有项目; >0: 特定项目
     */
    public $ProjectId;

    /**
     * @var string 安全组Id
     */
    public $SecurityGroupId;

    /**
     * @param integer $ProjectId 0:默认项目；-1 所有项目; >0: 特定项目
     * @param string $SecurityGroupId 安全组Id
     */
    function __construct()
    {

    }

    /**
     * For internal only. DO NOT USE IT.
     */
    public function deserialize($param)
    {
        if ($param === null) {
            return;
        }
        if (array_key_exists("ProjectId",$param) and $param["ProjectId"] !== null) {
            $this->ProjectId = $param["ProjectId"];
        }

        if (array_key_exists("SecurityGroupId",$param) and $param["SecurityGroupId"] !== null) {
            $this->SecurityGroupId = $param["SecurityGroupId"];
        }
    }
}
