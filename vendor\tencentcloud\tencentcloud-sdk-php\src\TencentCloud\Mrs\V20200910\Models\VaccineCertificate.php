<?php
/*
 * Copyright (c) 2017-2018 THL A29 Limited, a Tencent company. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
namespace TencentCloud\Mrs\*********\Models;
use TencentCloud\Common\AbstractModel;

/**
 * 免疫接种证明
 *
 * @method array getVaccineList() 获取免疫接种列表
注意：此字段可能返回 null，表示取不到有效值。
 * @method void setVaccineList(array $VaccineList) 设置免疫接种列表
注意：此字段可能返回 null，表示取不到有效值。
 */
class VaccineCertificate extends AbstractModel
{
    /**
     * @var array 免疫接种列表
注意：此字段可能返回 null，表示取不到有效值。
     */
    public $VaccineList;

    /**
     * @param array $VaccineList 免疫接种列表
注意：此字段可能返回 null，表示取不到有效值。
     */
    function __construct()
    {

    }

    /**
     * For internal only. DO NOT USE IT.
     */
    public function deserialize($param)
    {
        if ($param === null) {
            return;
        }
        if (array_key_exists("VaccineList",$param) and $param["VaccineList"] !== null) {
            $this->VaccineList = [];
            foreach ($param["VaccineList"] as $key => $value){
                $obj = new Vaccination();
                $obj->deserialize($value);
                array_push($this->VaccineList, $obj);
            }
        }
    }
}
