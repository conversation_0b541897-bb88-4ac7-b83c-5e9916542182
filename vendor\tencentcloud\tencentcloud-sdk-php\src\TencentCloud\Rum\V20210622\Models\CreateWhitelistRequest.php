<?php
/*
 * Copyright (c) 2017-2018 THL A29 Limited, a Tencent company. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
namespace TencentCloud\Rum\*********\Models;
use TencentCloud\Common\AbstractModel;

/**
 * CreateWhitelist请求参数结构体
 *
 * @method string getInstanceID() 获取实例ID：taw-123
 * @method void setInstanceID(string $InstanceID) 设置实例ID：taw-123
 * @method string getRemark() 获取备注（暂未作字节数限制）
 * @method void setRemark(string $Remark) 设置备注（暂未作字节数限制）
 * @method string getWhitelistUin() 获取uin：业务方标识
 * @method void setWhitelistUin(string $WhitelistUin) 设置uin：业务方标识
 * @method string getAid() 获取业务方标识
 * @method void setAid(string $Aid) 设置业务方标识
 */
class CreateWhitelistRequest extends AbstractModel
{
    /**
     * @var string 实例ID：taw-123
     */
    public $InstanceID;

    /**
     * @var string 备注（暂未作字节数限制）
     */
    public $Remark;

    /**
     * @var string uin：业务方标识
     */
    public $WhitelistUin;

    /**
     * @var string 业务方标识
     */
    public $Aid;

    /**
     * @param string $InstanceID 实例ID：taw-123
     * @param string $Remark 备注（暂未作字节数限制）
     * @param string $WhitelistUin uin：业务方标识
     * @param string $Aid 业务方标识
     */
    function __construct()
    {

    }

    /**
     * For internal only. DO NOT USE IT.
     */
    public function deserialize($param)
    {
        if ($param === null) {
            return;
        }
        if (array_key_exists("InstanceID",$param) and $param["InstanceID"] !== null) {
            $this->InstanceID = $param["InstanceID"];
        }

        if (array_key_exists("Remark",$param) and $param["Remark"] !== null) {
            $this->Remark = $param["Remark"];
        }

        if (array_key_exists("WhitelistUin",$param) and $param["WhitelistUin"] !== null) {
            $this->WhitelistUin = $param["WhitelistUin"];
        }

        if (array_key_exists("Aid",$param) and $param["Aid"] !== null) {
            $this->Aid = $param["Aid"];
        }
    }
}
