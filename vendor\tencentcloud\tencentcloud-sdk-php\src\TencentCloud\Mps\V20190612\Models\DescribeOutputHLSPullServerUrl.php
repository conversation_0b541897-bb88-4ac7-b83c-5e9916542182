<?php
/*
 * Copyright (c) 2017-2018 THL A29 Limited, a Tencent company. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
namespace TencentCloud\Mps\*********\Models;
use TencentCloud\Common\AbstractModel;

/**
 * 查询输出的HLS拉流URL信息。
 *
 * @method string getUrl() 获取HLS拉流地址的Url。
 * @method void setUrl(string $Url) 设置HLS拉流地址的Url。
 */
class DescribeOutputHLSPullServerUrl extends AbstractModel
{
    /**
     * @var string HLS拉流地址的Url。
     */
    public $Url;

    /**
     * @param string $Url HLS拉流地址的Url。
     */
    function __construct()
    {

    }

    /**
     * For internal only. DO NOT USE IT.
     */
    public function deserialize($param)
    {
        if ($param === null) {
            return;
        }
        if (array_key_exists("Url",$param) and $param["Url"] !== null) {
            $this->Url = $param["Url"];
        }
    }
}
