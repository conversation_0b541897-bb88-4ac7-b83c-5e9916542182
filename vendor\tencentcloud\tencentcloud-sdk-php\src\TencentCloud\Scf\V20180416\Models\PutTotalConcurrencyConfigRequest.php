<?php
/*
 * Copyright (c) 2017-2018 THL A29 Limited, a Tencent company. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
namespace TencentCloud\Scf\*********\Models;
use TencentCloud\Common\AbstractModel;

/**
 * PutTotalConcurrencyConfig请求参数结构体
 *
 * @method integer getTotalConcurrencyMem() 获取账号并发内存配额，注：账号并发内存配额下限：用户已用并发内存总额 + 12800
 * @method void setTotalConcurrencyMem(integer $TotalConcurrencyMem) 设置账号并发内存配额，注：账号并发内存配额下限：用户已用并发内存总额 + 12800
 * @method string getNamespace() 获取命名空间，默认为default
 * @method void setNamespace(string $Namespace) 设置命名空间，默认为default
 */
class PutTotalConcurrencyConfigRequest extends AbstractModel
{
    /**
     * @var integer 账号并发内存配额，注：账号并发内存配额下限：用户已用并发内存总额 + 12800
     */
    public $TotalConcurrencyMem;

    /**
     * @var string 命名空间，默认为default
     */
    public $Namespace;

    /**
     * @param integer $TotalConcurrencyMem 账号并发内存配额，注：账号并发内存配额下限：用户已用并发内存总额 + 12800
     * @param string $Namespace 命名空间，默认为default
     */
    function __construct()
    {

    }

    /**
     * For internal only. DO NOT USE IT.
     */
    public function deserialize($param)
    {
        if ($param === null) {
            return;
        }
        if (array_key_exists("TotalConcurrencyMem",$param) and $param["TotalConcurrencyMem"] !== null) {
            $this->TotalConcurrencyMem = $param["TotalConcurrencyMem"];
        }

        if (array_key_exists("Namespace",$param) and $param["Namespace"] !== null) {
            $this->Namespace = $param["Namespace"];
        }
    }
}
