<?php
/*
 * Copyright (c) 2017-2018 THL A29 Limited, a Tencent company. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
namespace TencentCloud\Redis\*********\Models;
use TencentCloud\Common\AbstractModel;

/**
 * DescribeInstances请求参数结构体
 *
 * @method integer getLimit() 获取每页输出实例的数量，参数默认值20，最大值为1000。
 * @method void setLimit(integer $Limit) 设置每页输出实例的数量，参数默认值20，最大值为1000。
 * @method integer getOffset() 获取分页偏移量，取Limit整数倍。计算公式：offset=limit*(页码-1)。
 * @method void setOffset(integer $Offset) 设置分页偏移量，取Limit整数倍。计算公式：offset=limit*(页码-1)。
 * @method string getInstanceId() 获取指定实例 ID。例如：crs-xjhsdj****。请登录[Redis控制台](https://console.cloud.tencent.com/redis)在实例列表复制实例 ID。


 * @method void setInstanceId(string $InstanceId) 设置指定实例 ID。例如：crs-xjhsdj****。请登录[Redis控制台](https://console.cloud.tencent.com/redis)在实例列表复制实例 ID。


 * @method string getOrderBy() 获取实例列表排序依据，枚举值如下所示：
- projectId：依据项目ID排序。
- createtime：依据实例创建时间排序。
- instancename：依据实例名称排序。
- type：依据实例类型排序。
- curDeadline：依据实例到期时间排序。
 * @method void setOrderBy(string $OrderBy) 设置实例列表排序依据，枚举值如下所示：
- projectId：依据项目ID排序。
- createtime：依据实例创建时间排序。
- instancename：依据实例名称排序。
- type：依据实例类型排序。
- curDeadline：依据实例到期时间排序。
 * @method integer getOrderType() 获取实例排序方式，默认为倒序排序。
- 1：倒序。
- 0：顺序。
 * @method void setOrderType(integer $OrderType) 设置实例排序方式，默认为倒序排序。
- 1：倒序。
- 0：顺序。
 * @method array getVpcIds() 获取私有网络 ID 数组。如果不配置该参数或设置数组为空则默认选择基础网络。例如47525。该参数暂时保留，可忽略。请根据 UniqVpcIds 参数格式设置私有网络ID数组。
 * @method void setVpcIds(array $VpcIds) 设置私有网络 ID 数组。如果不配置该参数或设置数组为空则默认选择基础网络。例如47525。该参数暂时保留，可忽略。请根据 UniqVpcIds 参数格式设置私有网络ID数组。
 * @method array getSubnetIds() 获取私有网络所属子网 ID 数组，例如：56854。该参数暂时保留，可忽略。请根据 UniqSubnetIds 参数格式设置私有网络子网 ID 数组。
 * @method void setSubnetIds(array $SubnetIds) 设置私有网络所属子网 ID 数组，例如：56854。该参数暂时保留，可忽略。请根据 UniqSubnetIds 参数格式设置私有网络子网 ID 数组。
 * @method string getSearchKey() 获取设置模糊查询关键字段，仅实例名称支持模糊查询。
 * @method void setSearchKey(string $SearchKey) 设置设置模糊查询关键字段，仅实例名称支持模糊查询。
 * @method array getProjectIds() 获取项目 ID 组成的数组。
 * @method void setProjectIds(array $ProjectIds) 设置项目 ID 组成的数组。
 * @method string getInstanceName() 获取实例名称。
 * @method void setInstanceName(string $InstanceName) 设置实例名称。
 * @method array getUniqVpcIds() 获取私有网络 ID 数组。如果不配置该参数或者设置数组为空则默认选择基础网络，如：vpc-sad23jfdfk。
 * @method void setUniqVpcIds(array $UniqVpcIds) 设置私有网络 ID 数组。如果不配置该参数或者设置数组为空则默认选择基础网络，如：vpc-sad23jfdfk。
 * @method array getUniqSubnetIds() 获取私有网络所属子网 ID 数组，如：subnet-fdj24n34j2。
 * @method void setUniqSubnetIds(array $UniqSubnetIds) 设置私有网络所属子网 ID 数组，如：subnet-fdj24n34j2。
 * @method array getRegionIds() 获取地域 ID 数组，该参数已经弃用，可通过公共参数Region查询对应地域。
 * @method void setRegionIds(array $RegionIds) 设置地域 ID 数组，该参数已经弃用，可通过公共参数Region查询对应地域。
 * @method array getStatus() 获取实例状态。
- 0：待初始化。
- 1：流程中。
- 2：运行中。
- -2：已隔离。
- -3：待删除。
 * @method void setStatus(array $Status) 设置实例状态。
- 0：待初始化。
- 1：流程中。
- 2：运行中。
- -2：已隔离。
- -3：待删除。
 * @method integer getTypeVersion() 获取实例架构版本。
- 1：单机版。
- 2：主从版。
- 3：集群版。
 * @method void setTypeVersion(integer $TypeVersion) 设置实例架构版本。
- 1：单机版。
- 2：主从版。
- 3：集群版。
 * @method string getEngineName() 获取存储引擎信息。可设置为Redis-2.8、Redis-4.0、Redis-5.0、Redis-6.0 或者 CKV。
 * @method void setEngineName(string $EngineName) 设置存储引擎信息。可设置为Redis-2.8、Redis-4.0、Redis-5.0、Redis-6.0 或者 CKV。
 * @method array getAutoRenew() 获取续费模式。
- 0：手动续费。
- 1：自动续费。
- 2：到期不再续费。
 * @method void setAutoRenew(array $AutoRenew) 设置续费模式。
- 0：手动续费。
- 1：自动续费。
- 2：到期不再续费。
 * @method string getBillingMode() 获取计费模式。
- postpaid：按量计费。
- prepaid：包年包月。
 * @method void setBillingMode(string $BillingMode) 设置计费模式。
- postpaid：按量计费。
- prepaid：包年包月。
 * @method integer getType() 获取实例类型。
- 2：Redis 2.8内存版（标准架构）。
- 3：CKV 3.2内存版（标准架构）。
- 4：CKV 3.2内存版（集群架构）。
- 5：Redis 2.8内存版（单机）。
- 6：Redis 4.0内存版（标准架构）。
- 7：Redis 4.0内存版（集群架构）。
- 8：Redis 5.0内存版（标准架构）。
- 9：Redis 5.0内存版（集群架构）。
- 15：Redis 6.2内存版（标准架构）。
- 16：Redis 6.2内存版（集群架构）。
 * @method void setType(integer $Type) 设置实例类型。
- 2：Redis 2.8内存版（标准架构）。
- 3：CKV 3.2内存版（标准架构）。
- 4：CKV 3.2内存版（集群架构）。
- 5：Redis 2.8内存版（单机）。
- 6：Redis 4.0内存版（标准架构）。
- 7：Redis 4.0内存版（集群架构）。
- 8：Redis 5.0内存版（标准架构）。
- 9：Redis 5.0内存版（集群架构）。
- 15：Redis 6.2内存版（标准架构）。
- 16：Redis 6.2内存版（集群架构）。
 * @method array getSearchKeys() 获取该参数为数组类型，支持配置实例名称、实例 ID、IP地址，其中实例名称为模糊匹配，实例 ID 和 IP 地址精确匹配。

- 数组中每一个元素取并集进行匹配查询。
- **InstanceId** 与 **SearchKeys** 同时配置，则取二者交集进行匹配查询。
 * @method void setSearchKeys(array $SearchKeys) 设置该参数为数组类型，支持配置实例名称、实例 ID、IP地址，其中实例名称为模糊匹配，实例 ID 和 IP 地址精确匹配。

- 数组中每一个元素取并集进行匹配查询。
- **InstanceId** 与 **SearchKeys** 同时配置，则取二者交集进行匹配查询。
 * @method array getTypeList() 获取内部参数，用户可忽略。
 * @method void setTypeList(array $TypeList) 设置内部参数，用户可忽略。
 * @method string getMonitorVersion() 获取内部参数，用户可忽略。
 * @method void setMonitorVersion(string $MonitorVersion) 设置内部参数，用户可忽略。
 * @method array getInstanceTags() 获取根据标签的 Key 和 Value 筛选资源。该参数不配置或者数组设置为空值，则不根据标签进行过滤。
 * @method void setInstanceTags(array $InstanceTags) 设置根据标签的 Key 和 Value 筛选资源。该参数不配置或者数组设置为空值，则不根据标签进行过滤。
 * @method array getTagKeys() 获取根据标签的 Key 筛选资源，该参数不配置或者数组设置为空值，则不根据标签Key进行过滤。
 * @method void setTagKeys(array $TagKeys) 设置根据标签的 Key 筛选资源，该参数不配置或者数组设置为空值，则不根据标签Key进行过滤。
 * @method array getProductVersions() 获取实例的产品版本。如果该参数不配置或者数组设置为空值，则默认不依据此参数过滤实例。
- local：本地盘版。
- cdc：独享集群版。
 * @method void setProductVersions(array $ProductVersions) 设置实例的产品版本。如果该参数不配置或者数组设置为空值，则默认不依据此参数过滤实例。
- local：本地盘版。
- cdc：独享集群版。
 * @method array getInstanceIds() 获取批量查询指定的实例 ID，返回结果已 Limit 限制为主。
 * @method void setInstanceIds(array $InstanceIds) 设置批量查询指定的实例 ID，返回结果已 Limit 限制为主。
 * @method string getAzMode() 获取可用区模式。
- singleaz：单可用区。
- multiaz：多可用区。
 * @method void setAzMode(string $AzMode) 设置可用区模式。
- singleaz：单可用区。
- multiaz：多可用区。
 */
class DescribeInstancesRequest extends AbstractModel
{
    /**
     * @var integer 每页输出实例的数量，参数默认值20，最大值为1000。
     */
    public $Limit;

    /**
     * @var integer 分页偏移量，取Limit整数倍。计算公式：offset=limit*(页码-1)。
     */
    public $Offset;

    /**
     * @var string 指定实例 ID。例如：crs-xjhsdj****。请登录[Redis控制台](https://console.cloud.tencent.com/redis)在实例列表复制实例 ID。


     */
    public $InstanceId;

    /**
     * @var string 实例列表排序依据，枚举值如下所示：
- projectId：依据项目ID排序。
- createtime：依据实例创建时间排序。
- instancename：依据实例名称排序。
- type：依据实例类型排序。
- curDeadline：依据实例到期时间排序。
     */
    public $OrderBy;

    /**
     * @var integer 实例排序方式，默认为倒序排序。
- 1：倒序。
- 0：顺序。
     */
    public $OrderType;

    /**
     * @var array 私有网络 ID 数组。如果不配置该参数或设置数组为空则默认选择基础网络。例如47525。该参数暂时保留，可忽略。请根据 UniqVpcIds 参数格式设置私有网络ID数组。
     */
    public $VpcIds;

    /**
     * @var array 私有网络所属子网 ID 数组，例如：56854。该参数暂时保留，可忽略。请根据 UniqSubnetIds 参数格式设置私有网络子网 ID 数组。
     */
    public $SubnetIds;

    /**
     * @var string 设置模糊查询关键字段，仅实例名称支持模糊查询。
     */
    public $SearchKey;

    /**
     * @var array 项目 ID 组成的数组。
     */
    public $ProjectIds;

    /**
     * @var string 实例名称。
     */
    public $InstanceName;

    /**
     * @var array 私有网络 ID 数组。如果不配置该参数或者设置数组为空则默认选择基础网络，如：vpc-sad23jfdfk。
     */
    public $UniqVpcIds;

    /**
     * @var array 私有网络所属子网 ID 数组，如：subnet-fdj24n34j2。
     */
    public $UniqSubnetIds;

    /**
     * @var array 地域 ID 数组，该参数已经弃用，可通过公共参数Region查询对应地域。
     */
    public $RegionIds;

    /**
     * @var array 实例状态。
- 0：待初始化。
- 1：流程中。
- 2：运行中。
- -2：已隔离。
- -3：待删除。
     */
    public $Status;

    /**
     * @var integer 实例架构版本。
- 1：单机版。
- 2：主从版。
- 3：集群版。
     */
    public $TypeVersion;

    /**
     * @var string 存储引擎信息。可设置为Redis-2.8、Redis-4.0、Redis-5.0、Redis-6.0 或者 CKV。
     */
    public $EngineName;

    /**
     * @var array 续费模式。
- 0：手动续费。
- 1：自动续费。
- 2：到期不再续费。
     */
    public $AutoRenew;

    /**
     * @var string 计费模式。
- postpaid：按量计费。
- prepaid：包年包月。
     */
    public $BillingMode;

    /**
     * @var integer 实例类型。
- 2：Redis 2.8内存版（标准架构）。
- 3：CKV 3.2内存版（标准架构）。
- 4：CKV 3.2内存版（集群架构）。
- 5：Redis 2.8内存版（单机）。
- 6：Redis 4.0内存版（标准架构）。
- 7：Redis 4.0内存版（集群架构）。
- 8：Redis 5.0内存版（标准架构）。
- 9：Redis 5.0内存版（集群架构）。
- 15：Redis 6.2内存版（标准架构）。
- 16：Redis 6.2内存版（集群架构）。
     */
    public $Type;

    /**
     * @var array 该参数为数组类型，支持配置实例名称、实例 ID、IP地址，其中实例名称为模糊匹配，实例 ID 和 IP 地址精确匹配。

- 数组中每一个元素取并集进行匹配查询。
- **InstanceId** 与 **SearchKeys** 同时配置，则取二者交集进行匹配查询。
     */
    public $SearchKeys;

    /**
     * @var array 内部参数，用户可忽略。
     */
    public $TypeList;

    /**
     * @var string 内部参数，用户可忽略。
     */
    public $MonitorVersion;

    /**
     * @var array 根据标签的 Key 和 Value 筛选资源。该参数不配置或者数组设置为空值，则不根据标签进行过滤。
     */
    public $InstanceTags;

    /**
     * @var array 根据标签的 Key 筛选资源，该参数不配置或者数组设置为空值，则不根据标签Key进行过滤。
     */
    public $TagKeys;

    /**
     * @var array 实例的产品版本。如果该参数不配置或者数组设置为空值，则默认不依据此参数过滤实例。
- local：本地盘版。
- cdc：独享集群版。
     */
    public $ProductVersions;

    /**
     * @var array 批量查询指定的实例 ID，返回结果已 Limit 限制为主。
     */
    public $InstanceIds;

    /**
     * @var string 可用区模式。
- singleaz：单可用区。
- multiaz：多可用区。
     */
    public $AzMode;

    /**
     * @param integer $Limit 每页输出实例的数量，参数默认值20，最大值为1000。
     * @param integer $Offset 分页偏移量，取Limit整数倍。计算公式：offset=limit*(页码-1)。
     * @param string $InstanceId 指定实例 ID。例如：crs-xjhsdj****。请登录[Redis控制台](https://console.cloud.tencent.com/redis)在实例列表复制实例 ID。


     * @param string $OrderBy 实例列表排序依据，枚举值如下所示：
- projectId：依据项目ID排序。
- createtime：依据实例创建时间排序。
- instancename：依据实例名称排序。
- type：依据实例类型排序。
- curDeadline：依据实例到期时间排序。
     * @param integer $OrderType 实例排序方式，默认为倒序排序。
- 1：倒序。
- 0：顺序。
     * @param array $VpcIds 私有网络 ID 数组。如果不配置该参数或设置数组为空则默认选择基础网络。例如47525。该参数暂时保留，可忽略。请根据 UniqVpcIds 参数格式设置私有网络ID数组。
     * @param array $SubnetIds 私有网络所属子网 ID 数组，例如：56854。该参数暂时保留，可忽略。请根据 UniqSubnetIds 参数格式设置私有网络子网 ID 数组。
     * @param string $SearchKey 设置模糊查询关键字段，仅实例名称支持模糊查询。
     * @param array $ProjectIds 项目 ID 组成的数组。
     * @param string $InstanceName 实例名称。
     * @param array $UniqVpcIds 私有网络 ID 数组。如果不配置该参数或者设置数组为空则默认选择基础网络，如：vpc-sad23jfdfk。
     * @param array $UniqSubnetIds 私有网络所属子网 ID 数组，如：subnet-fdj24n34j2。
     * @param array $RegionIds 地域 ID 数组，该参数已经弃用，可通过公共参数Region查询对应地域。
     * @param array $Status 实例状态。
- 0：待初始化。
- 1：流程中。
- 2：运行中。
- -2：已隔离。
- -3：待删除。
     * @param integer $TypeVersion 实例架构版本。
- 1：单机版。
- 2：主从版。
- 3：集群版。
     * @param string $EngineName 存储引擎信息。可设置为Redis-2.8、Redis-4.0、Redis-5.0、Redis-6.0 或者 CKV。
     * @param array $AutoRenew 续费模式。
- 0：手动续费。
- 1：自动续费。
- 2：到期不再续费。
     * @param string $BillingMode 计费模式。
- postpaid：按量计费。
- prepaid：包年包月。
     * @param integer $Type 实例类型。
- 2：Redis 2.8内存版（标准架构）。
- 3：CKV 3.2内存版（标准架构）。
- 4：CKV 3.2内存版（集群架构）。
- 5：Redis 2.8内存版（单机）。
- 6：Redis 4.0内存版（标准架构）。
- 7：Redis 4.0内存版（集群架构）。
- 8：Redis 5.0内存版（标准架构）。
- 9：Redis 5.0内存版（集群架构）。
- 15：Redis 6.2内存版（标准架构）。
- 16：Redis 6.2内存版（集群架构）。
     * @param array $SearchKeys 该参数为数组类型，支持配置实例名称、实例 ID、IP地址，其中实例名称为模糊匹配，实例 ID 和 IP 地址精确匹配。

- 数组中每一个元素取并集进行匹配查询。
- **InstanceId** 与 **SearchKeys** 同时配置，则取二者交集进行匹配查询。
     * @param array $TypeList 内部参数，用户可忽略。
     * @param string $MonitorVersion 内部参数，用户可忽略。
     * @param array $InstanceTags 根据标签的 Key 和 Value 筛选资源。该参数不配置或者数组设置为空值，则不根据标签进行过滤。
     * @param array $TagKeys 根据标签的 Key 筛选资源，该参数不配置或者数组设置为空值，则不根据标签Key进行过滤。
     * @param array $ProductVersions 实例的产品版本。如果该参数不配置或者数组设置为空值，则默认不依据此参数过滤实例。
- local：本地盘版。
- cdc：独享集群版。
     * @param array $InstanceIds 批量查询指定的实例 ID，返回结果已 Limit 限制为主。
     * @param string $AzMode 可用区模式。
- singleaz：单可用区。
- multiaz：多可用区。
     */
    function __construct()
    {

    }

    /**
     * For internal only. DO NOT USE IT.
     */
    public function deserialize($param)
    {
        if ($param === null) {
            return;
        }
        if (array_key_exists("Limit",$param) and $param["Limit"] !== null) {
            $this->Limit = $param["Limit"];
        }

        if (array_key_exists("Offset",$param) and $param["Offset"] !== null) {
            $this->Offset = $param["Offset"];
        }

        if (array_key_exists("InstanceId",$param) and $param["InstanceId"] !== null) {
            $this->InstanceId = $param["InstanceId"];
        }

        if (array_key_exists("OrderBy",$param) and $param["OrderBy"] !== null) {
            $this->OrderBy = $param["OrderBy"];
        }

        if (array_key_exists("OrderType",$param) and $param["OrderType"] !== null) {
            $this->OrderType = $param["OrderType"];
        }

        if (array_key_exists("VpcIds",$param) and $param["VpcIds"] !== null) {
            $this->VpcIds = $param["VpcIds"];
        }

        if (array_key_exists("SubnetIds",$param) and $param["SubnetIds"] !== null) {
            $this->SubnetIds = $param["SubnetIds"];
        }

        if (array_key_exists("SearchKey",$param) and $param["SearchKey"] !== null) {
            $this->SearchKey = $param["SearchKey"];
        }

        if (array_key_exists("ProjectIds",$param) and $param["ProjectIds"] !== null) {
            $this->ProjectIds = $param["ProjectIds"];
        }

        if (array_key_exists("InstanceName",$param) and $param["InstanceName"] !== null) {
            $this->InstanceName = $param["InstanceName"];
        }

        if (array_key_exists("UniqVpcIds",$param) and $param["UniqVpcIds"] !== null) {
            $this->UniqVpcIds = $param["UniqVpcIds"];
        }

        if (array_key_exists("UniqSubnetIds",$param) and $param["UniqSubnetIds"] !== null) {
            $this->UniqSubnetIds = $param["UniqSubnetIds"];
        }

        if (array_key_exists("RegionIds",$param) and $param["RegionIds"] !== null) {
            $this->RegionIds = $param["RegionIds"];
        }

        if (array_key_exists("Status",$param) and $param["Status"] !== null) {
            $this->Status = $param["Status"];
        }

        if (array_key_exists("TypeVersion",$param) and $param["TypeVersion"] !== null) {
            $this->TypeVersion = $param["TypeVersion"];
        }

        if (array_key_exists("EngineName",$param) and $param["EngineName"] !== null) {
            $this->EngineName = $param["EngineName"];
        }

        if (array_key_exists("AutoRenew",$param) and $param["AutoRenew"] !== null) {
            $this->AutoRenew = $param["AutoRenew"];
        }

        if (array_key_exists("BillingMode",$param) and $param["BillingMode"] !== null) {
            $this->BillingMode = $param["BillingMode"];
        }

        if (array_key_exists("Type",$param) and $param["Type"] !== null) {
            $this->Type = $param["Type"];
        }

        if (array_key_exists("SearchKeys",$param) and $param["SearchKeys"] !== null) {
            $this->SearchKeys = $param["SearchKeys"];
        }

        if (array_key_exists("TypeList",$param) and $param["TypeList"] !== null) {
            $this->TypeList = $param["TypeList"];
        }

        if (array_key_exists("MonitorVersion",$param) and $param["MonitorVersion"] !== null) {
            $this->MonitorVersion = $param["MonitorVersion"];
        }

        if (array_key_exists("InstanceTags",$param) and $param["InstanceTags"] !== null) {
            $this->InstanceTags = [];
            foreach ($param["InstanceTags"] as $key => $value){
                $obj = new InstanceTagInfo();
                $obj->deserialize($value);
                array_push($this->InstanceTags, $obj);
            }
        }

        if (array_key_exists("TagKeys",$param) and $param["TagKeys"] !== null) {
            $this->TagKeys = $param["TagKeys"];
        }

        if (array_key_exists("ProductVersions",$param) and $param["ProductVersions"] !== null) {
            $this->ProductVersions = $param["ProductVersions"];
        }

        if (array_key_exists("InstanceIds",$param) and $param["InstanceIds"] !== null) {
            $this->InstanceIds = $param["InstanceIds"];
        }

        if (array_key_exists("AzMode",$param) and $param["AzMode"] !== null) {
            $this->AzMode = $param["AzMode"];
        }
    }
}
