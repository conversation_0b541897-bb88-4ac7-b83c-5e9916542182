<?php
/*
 * Copyright (c) 2017-2018 THL A29 Limited, a Tencent company. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
namespace TencentCloud\Mrs\*********\Models;
use TencentCloud\Common\AbstractModel;

/**
 * 体检报告PDF信息
 *
 * @method string getUrl() 获取pdf文件url链接(暂不支持)
 * @method void setUrl(string $Url) 设置pdf文件url链接(暂不支持)
 * @method string getBase64() 获取pdf文件base64编码字符串
 * @method void setBase64(string $Base64) 设置pdf文件base64编码字符串
 */
class PdfInfo extends AbstractModel
{
    /**
     * @var string pdf文件url链接(暂不支持)
     */
    public $Url;

    /**
     * @var string pdf文件base64编码字符串
     */
    public $Base64;

    /**
     * @param string $Url pdf文件url链接(暂不支持)
     * @param string $Base64 pdf文件base64编码字符串
     */
    function __construct()
    {

    }

    /**
     * For internal only. DO NOT USE IT.
     */
    public function deserialize($param)
    {
        if ($param === null) {
            return;
        }
        if (array_key_exists("Url",$param) and $param["Url"] !== null) {
            $this->Url = $param["Url"];
        }

        if (array_key_exists("Base64",$param) and $param["Base64"] !== null) {
            $this->Base64 = $param["Base64"];
        }
    }
}
