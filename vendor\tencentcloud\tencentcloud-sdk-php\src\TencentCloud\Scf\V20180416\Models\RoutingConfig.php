<?php
/*
 * Copyright (c) 2017-2018 THL A29 Limited, a Tencent company. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
namespace TencentCloud\Scf\*********\Models;
use TencentCloud\Common\AbstractModel;

/**
 * 别名的版本路由配置
其中：随机权重路由附加版本和规则路由附加版本不可以同时配置
 *
 * @method array getAdditionalVersionWeights() 获取随机权重路由附加版本
 * @method void setAdditionalVersionWeights(array $AdditionalVersionWeights) 设置随机权重路由附加版本
 * @method array getAddtionVersionMatchs() 获取规则路由附加版本
 * @method void setAddtionVersionMatchs(array $AddtionVersionMatchs) 设置规则路由附加版本
 */
class RoutingConfig extends AbstractModel
{
    /**
     * @var array 随机权重路由附加版本
     */
    public $AdditionalVersionWeights;

    /**
     * @var array 规则路由附加版本
     */
    public $AddtionVersionMatchs;

    /**
     * @param array $AdditionalVersionWeights 随机权重路由附加版本
     * @param array $AddtionVersionMatchs 规则路由附加版本
     */
    function __construct()
    {

    }

    /**
     * For internal only. DO NOT USE IT.
     */
    public function deserialize($param)
    {
        if ($param === null) {
            return;
        }
        if (array_key_exists("AdditionalVersionWeights",$param) and $param["AdditionalVersionWeights"] !== null) {
            $this->AdditionalVersionWeights = [];
            foreach ($param["AdditionalVersionWeights"] as $key => $value){
                $obj = new VersionWeight();
                $obj->deserialize($value);
                array_push($this->AdditionalVersionWeights, $obj);
            }
        }

        if (array_key_exists("AddtionVersionMatchs",$param) and $param["AddtionVersionMatchs"] !== null) {
            $this->AddtionVersionMatchs = [];
            foreach ($param["AddtionVersionMatchs"] as $key => $value){
                $obj = new VersionMatch();
                $obj->deserialize($value);
                array_push($this->AddtionVersionMatchs, $obj);
            }
        }
    }
}
